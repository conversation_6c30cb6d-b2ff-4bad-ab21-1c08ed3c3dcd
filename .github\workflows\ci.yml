name: CI

on:
  push:
    branches: ["main"]
  pull_request:
    types: [opened, synchronize]

jobs:
  build:
    name: Build and Test
    runs-on: ${{ matrix.os }}
    timeout-minutes: 15
    env:
      TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
      TURBO_TEAM: ${{ secrets.TURBO_TEAM }}
      CI: true
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest]
        node-version: [22.x]
    steps:
      - name: Check out code
        uses: actions/checkout@v4
        with:
          fetch-depth: 2

      - uses: pnpm/action-setup@v4
        with:
          version: 9.15.1

      - name: Setup Node.js environment
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: "pnpm"

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Install playwright
        run: pnpm exec playwright install --with-deps

      - name: Eslint
        run: pnpm lint

      - name: Typescript
        run: pnpm typecheck

      - name: Build
        run: pnpm build

      - name: Unit and e2e tests
        run: pnpm test

      - uses: actions/upload-artifact@v4
        if: always()
        with:
          name: playwright-report-${{ matrix.runs-on }}
          path: playwright-report-${{ matrix.runs-on }}/
          retention-days: 30
