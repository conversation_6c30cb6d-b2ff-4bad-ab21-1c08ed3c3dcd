name: Release

on:
  push:
    branches:
      - main

concurrency: ${{ github.workflow }}-${{ github.ref }}

jobs:
  release:
    name: Release
    runs-on: ubuntu-latest
    permissions:
      contents: write
      pull-requests: write
      id-token: write
    steps:
      - name: Checkout Repo
        uses: actions/checkout@v4
        with:
          # This makes Actions fetch all Git history so that Changesets can generate changelogs with the correct commits
          fetch-depth: 0

      - uses: pnpm/action-setup@v4
        with:
          version: 9.15.1

      - name: Setup Node.js environment
        uses: actions/setup-node@v4
        with:
          node-version: 22.11
          cache: "pnpm"

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Creating .npmrc
        run: |
          cat << EOF > "$HOME/.npmrc"
            email=<EMAIL>
            //registry.npmjs.org/:_authToken=$NPM_TOKEN
          EOF
        env:
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}

      - name: Create Release Pull Request or Publish to npm
        id: changesets
        uses: changesets/action@v1
        with:
          publish: pnpm release
        env:
          GITHUB_TOKEN: ${{ secrets.GIT_DEPLOY_KEY }}
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}

      - name: Publishing next version
        run: cd packages/ladle && pnpm types && ./publish-next.js
        env:
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
