{"name": "test-addons", "version": "0.2.79", "license": "MIT", "private": true, "type": "module", "scripts": {"serve": "ladle serve -p 61100", "serve-prod": "ladle preview -p 61100", "build": "ladle build", "lint": "echo 'no lint'", "test-dev": "cross-env TYPE=dev pnpm exec playwright test", "test-prod": "cross-env TYPE=prod pnpm exec playwright test", "test": "pnpm test-dev && pnpm test-prod"}, "dependencies": {"@ladle/playwright-config": "workspace:*", "@ladle/react": "workspace:*", "@playwright/test": "^1.49.1", "axe-playwright": "^2.0.3", "cross-env": "^7.0.3", "query-string": "^9.1.1", "react": "^19.0.0", "react-dom": "^19.0.0"}}