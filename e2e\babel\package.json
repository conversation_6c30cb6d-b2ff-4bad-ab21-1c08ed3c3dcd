{"name": "test-babel", "version": "0.3.51", "license": "MIT", "private": true, "scripts": {"serve": "ladle serve -p 61109", "serve-prod": "ladle preview -p 61109", "build": "ladle build", "lint": "echo 'no lint'", "test-dev": "cross-env TYPE=dev pnpm exec playwright test", "test-prod": "cross-env TYPE=prod pnpm exec playwright test", "test": "pnpm test-dev && pnpm test-prod"}, "dependencies": {"@ladle/playwright-config": "workspace:*", "@ladle/react": "workspace:*", "@playwright/test": "^1.49.1", "@vitejs/plugin-react": "^4.3.4", "cross-env": "^7.0.3", "react": "^19.0.0", "react-dom": "^19.0.0"}}