{"name": "test-msw", "version": "0.0.94", "license": "MIT", "private": true, "scripts": {"serve": "ladle serve -p 61112", "serve-prod": "ladle preview -p 61112", "build": "ladle build", "lint": "echo 'no lint'", "test-dev": "cross-env TYPE=dev pnpm exec playwright test", "test-prod": "cross-env TYPE=prod pnpm exec playwright test", "test": "pnpm test-dev && pnpm test-prod"}, "dependencies": {"@ladle/playwright-config": "workspace:*", "@ladle/react": "workspace:*", "@playwright/test": "^1.49.1", "autoprefixer": "^10.4.20", "baseui": "^14.0.0", "cross-env": "^7.0.3", "postcss": "^8.4.49", "react": "^19.0.0", "react-dom": "^19.0.0", "styletron-engine-monolithic": "^1.0.0", "styletron-react": "^6.1.1", "tailwindcss": "^3.4.17"}}