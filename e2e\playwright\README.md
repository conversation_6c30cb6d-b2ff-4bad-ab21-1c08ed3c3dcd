# Visual Snapshots with <PERSON><PERSON>

This package demonstrates how you can quickly automate visual snapshots with <PERSON><PERSON> and <PERSON><PERSON> to cover all your stories.

Read the [post](https://ladle.dev/blog/visual-snapshots) for more information. (The actual source code here a slightly different since it has a double purpose as an e2e test.)

## Run it

Clone this repo, navigate to this folder and run:

```sh
pnpm install
pnpm build #build ladle
pnpm test #run tests
pnpm test:update #update snapshots if there are changes
```
