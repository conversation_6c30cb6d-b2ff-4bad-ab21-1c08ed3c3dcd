{"name": "test-playwright", "version": "0.0.65", "license": "MIT", "private": true, "scripts": {"serve": "ladle serve -p 61110", "serve-prod": "ladle preview -p 61110", "build-preview": "ladle build && ladle preview -p 61110", "build": "ladle build", "lint": "echo 'no lint'", "test-dev": "cross-env TYPE=dev pnpm exec playwright test", "test-prod": "cross-env TYPE=prod pnpm exec playwright test", "test": "pnpm test-dev && pnpm test-prod", "test:update": "cross-env TYPE=update pnpm exec playwright test -u"}, "dependencies": {"@ladle/playwright-config": "workspace:*", "@ladle/react": "workspace:*", "@playwright/test": "^1.49.1", "@types/sync-fetch": "^0.4.3", "cross-env": "^7.0.3", "react": "^19.0.0", "react-dom": "^19.0.0", "start-server-and-test": "^2.0.9", "sync-fetch": "0.6.0-2"}}