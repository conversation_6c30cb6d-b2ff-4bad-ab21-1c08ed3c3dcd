{"name": "ladle", "version": "0.0.0", "main": "index.js", "repository": "**************:tajo/ladle.git", "author": "Vojtech Miksu <<EMAIL>>", "license": "MIT", "private": true, "packageManager": "pnpm@9.15.1", "scripts": {"lint": "eslint \"./{packages,e2e}/*/{lib,src,tests}/**/*.{ts,tsx}\" --max-warnings=0", "test": "turbo run test --concurrency=1", "build": "turbo run build", "typecheck": "tsc", "prepare": "husky", "release": "./release.sh"}, "workspaces": ["packages/example", "packages/ladle", "packages/website", "e2e/addons", "e2e/babel", "e2e/commonjs", "e2e/config", "e2e/config-ts", "e2e/css", "e2e/decorators", "e2e/playwright", "e2e/playwright-config", "e2e/programmatic", "e2e/provider", "e2e/baseweb", "e2e/msw"], "devDependencies": {"@changesets/changelog-github": "^0.5.0", "@changesets/cli": "^2.27.11", "@commitlint/cli": "^19.6.1", "@commitlint/config-conventional": "^19.6.0", "@eslint/js": "^9.17.0", "@playwright/test": "^1.49.1", "@types/eslint__js": "^8.42.3", "@types/react": "^19.0.2", "@types/react-dom": "^19.0.2", "eslint": "^9.17.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.37.2", "globals": "^15.14.0", "husky": "^9.1.7", "lint-staged": "^15.2.11", "prettier": "^3.4.2", "turbo": "^2.3.3", "typescript": "^5.7.2", "typescript-eslint": "^8.18.1"}, "engines": {"node": ">=22.0.0"}, "pnpm": {"peerDependencyRules": {"allowedVersions": {"@types/react": "19", "react": "19", "react-dom": "19", "typescript": "5"}}}}