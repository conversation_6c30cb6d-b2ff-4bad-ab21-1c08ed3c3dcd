<!DOCTYPE html>
<html lang="en-US">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta
      name="description"
      content="Ladle: Playground for your React Components."
    />
    <meta name="theme-color" content="#ffffff" />
    <link rel="stylesheet" type="text/css" href="/ladle.css" />
    <link rel="icon" href="/favicon.svg" />
    <link rel="mask-icon" href="/mask-icon.svg" color="#000000" />
    <link rel="apple-touch-icon" href="/touch-icon.png" />
    <link
      rel="manifest"
      href="/manifest.webmanifest"
      crossorigin="use-credentials"
    />
    <script type="module" src="/src/init-side-effects.ts"></script>
    <title>Ladle</title>
  </head>

  <body>
    <div class="ladle-background"></div>
    <div id="ladle-root" class="ladle-wrapper"></div>
    <script type="module" src="/src/index.tsx"></script>
  </body>
</html>
