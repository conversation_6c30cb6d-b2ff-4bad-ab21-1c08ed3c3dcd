{"name": "@ladle/react", "version": "5.0.3", "repository": "**************:tajo/ladle.git", "author": "Vojtech Miksu <<EMAIL>>", "license": "MIT", "type": "module", "types": "./lib/app/exports.ts", "exports": {".": "./lib/app/exports.ts", "./serve": "./api/serve.js", "./preview": "./api/preview.js", "./build": "./api/build.js", "./meta": "./api/meta.js", "./msw-node": "./api/msw-node.js"}, "packageManager": "pnpm@9.15.1", "publishConfig": {"access": "public", "provenance": true}, "files": ["lib", "api", "typings-for-build"], "bin": {"ladle": "./lib/cli/cli.js"}, "scripts": {"cli": "node ./lib/cli/cli.js", "clean": "rimraf dist && rimraf .ladle && rimraf build && rimraf *.tsbuildinfo", "serve": "node ./lib/cli/cli.js serve", "test": "cross-env IMPORT_ROOT=\"./\" vitest run", "test:watch": "cross-env IMPORT_ROOT=\"./\" vitest", "types": "tsc --project tsconfig.typesoutput.json && cp ./typings-for-build/app/exports.d.ts ./typings-for-build/app/exports.d.cts && cp -r -n ./lib/app ./typings-for-build/ || true && ./scripts/update-index-path.js"}, "dependencies": {"@babel/code-frame": "^7.26.2", "@babel/core": "^7.26.0", "@babel/generator": "^7.26.3", "@babel/parser": "^7.26.3", "@babel/template": "^7.25.9", "@babel/traverse": "^7.26.4", "@babel/types": "^7.26.3", "@ladle/react-context": "^1.0.1", "@mdx-js/mdx": "^3.1.0", "@mdx-js/react": "^3.1.0", "@vitejs/plugin-react": "^4.3.4", "@vitejs/plugin-react-swc": "^3.7.2", "axe-core": "^4.10.2", "boxen": "^8.0.1", "chokidar": "^4.0.3", "classnames": "^2.5.1", "commander": "^12.1.0", "cross-spawn": "^7.0.6", "debug": "^4.4.0", "get-port": "^7.1.0", "globby": "^14.0.2", "history": "^5.3.0", "koa": "^2.15.4", "koa-connect": "^2.1.0", "lodash.merge": "^4.6.2", "msw": "^2.7.0", "open": "^10.1.0", "prism-react-renderer": "^2.4.1", "prop-types": "^15.8.1", "query-string": "^9.1.1", "react-hotkeys-hook": "^4.6.1", "react-inspector": "^6.0.2", "rehype-class-names": "^2.0.0", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.0", "source-map": "^0.7.4", "vfile": "^6.0.3", "vite": "^6.0.5", "vite-tsconfig-paths": "^5.1.4"}, "peerDependencies": {"react": ">=18.0.0", "react-dom": ">=18.0.0"}, "devDependencies": {"@babel/cli": "^7.26.4", "@types/babel__code-frame": "^7.0.6", "@types/babel__core": "^7.20.5", "@types/babel__generator": "^7.6.8", "@types/babel__template": "^7.4.4", "@types/babel__traverse": "^7.20.6", "@types/cross-spawn": "^6.0.6", "@types/debug": "^4.1.12", "@types/express": "^5.0.0", "@types/koa": "^2.15.0", "@types/lodash.merge": "^4.6.9", "@types/node": "^22.10.2", "@types/ws": "^8.5.13", "cross-env": "^7.0.3", "react": "^19.0.0", "react-dom": "^19.0.0", "vitest": "^2.1.8"}, "engines": {"node": ">=20.0.0"}}