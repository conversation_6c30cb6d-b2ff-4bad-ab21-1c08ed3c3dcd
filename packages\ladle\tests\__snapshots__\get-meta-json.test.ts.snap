// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Capital letters in story names converted into delimiters 1`] = `
"{
  "about": {
    "homepage": "https://www.ladle.dev",
    "github": "https://github.com/tajo/ladle",
    "version": 1
  },
  "stories": {
    "capitalization--big-barking-dog": {
      "name": "Big barking dog",
      "levels": [
        "Capitalization"
      ],
      "locStart": 8,
      "locEnd": 10,
      "filePath": "tests/fixtures/capitalization.stories.tsx",
      "namedExport": "BigBarkingDog",
      "meta": {}
    },
    "capitalization--blue-tiny-cat": {
      "name": "Blue tiny cat",
      "levels": [
        "Capitalization"
      ],
      "locStart": 4,
      "locEnd": 6,
      "filePath": "tests/fixtures/capitalization.stories.tsx",
      "namedExport": "BlueTinyCat",
      "meta": {}
    }
  }
}"
`;

exports[`Capital letters in the filename converted into delimiters 1`] = `
"{
  "about": {
    "homepage": "https://www.ladle.dev",
    "github": "https://github.com/tajo/ladle",
    "version": 1
  },
  "stories": {
    "filename-capitalization--test": {
      "name": "Test",
      "levels": [
        "Filename capitalization"
      ],
      "locStart": 4,
      "locEnd": 6,
      "filePath": "tests/fixtures/filenameCapitalization.stories.tsx",
      "namedExport": "Test",
      "meta": {}
    }
  }
}"
`;

exports[`Default title is used instead of the file name 1`] = `
"{
  "about": {
    "homepage": "https://www.ladle.dev",
    "github": "https://github.com/tajo/ladle",
    "version": 1
  },
  "stories": {
    "title--cat": {
      "name": "Cat",
      "levels": [
        "Title"
      ],
      "locStart": 8,
      "locEnd": 10,
      "filePath": "tests/fixtures/default-title.stories.tsx",
      "namedExport": "Cat",
      "meta": {}
    }
  }
}"
`;

exports[`Extract and merge story meta 1`] = `
"{
  "about": {
    "homepage": "https://www.ladle.dev",
    "github": "https://github.com/tajo/ladle",
    "version": 1
  },
  "stories": {
    "title--cat": {
      "name": "Cat",
      "levels": [
        "Title"
      ],
      "locStart": 14,
      "locEnd": 16,
      "filePath": "tests/fixtures/story-meta.stories.tsx",
      "namedExport": "Cat",
      "meta": {
        "baseweb": {
          "theme": "dark",
          "browsers": [
            "chrome",
            "firefox"
          ],
          "width": "500px"
        },
        "links": true
      }
    },
    "title--dog": {
      "name": "Dog",
      "levels": [
        "Title"
      ],
      "locStart": 26,
      "locEnd": 28,
      "filePath": "tests/fixtures/story-meta.stories.tsx",
      "namedExport": "Dog",
      "meta": {
        "baseweb": {
          "theme": "dark",
          "browsers": [
            "chrome",
            "webkit"
          ]
        }
      }
    }
  }
}"
`;

exports[`Extract default meta 1`] = `
"{
  "about": {
    "homepage": "https://www.ladle.dev",
    "github": "https://github.com/tajo/ladle",
    "version": 1
  },
  "stories": {
    "title--cat": {
      "name": "Cat",
      "levels": [
        "Title"
      ],
      "locStart": 13,
      "locEnd": 15,
      "filePath": "tests/fixtures/default-meta.stories.tsx",
      "namedExport": "Cat",
      "meta": {
        "baseweb": {
          "foo": "title"
        }
      }
    }
  }
}"
`;

exports[`Single file with two stories 1`] = `
"{
  "about": {
    "homepage": "https://www.ladle.dev",
    "github": "https://github.com/tajo/ladle",
    "version": 1
  },
  "stories": {
    "animals--cat": {
      "name": "Cat",
      "levels": [
        "Animals"
      ],
      "locStart": 4,
      "locEnd": 6,
      "filePath": "tests/fixtures/animals.stories.tsx",
      "namedExport": "Cat",
      "meta": {}
    },
    "animals--dog": {
      "name": "Dog",
      "levels": [
        "Animals"
      ],
      "locStart": 8,
      "locEnd": 10,
      "filePath": "tests/fixtures/animals.stories.tsx",
      "namedExport": "Dog",
      "meta": {}
    }
  }
}"
`;

exports[`Story name replaces named export as a story name 1`] = `
"{
  "about": {
    "homepage": "https://www.ladle.dev",
    "github": "https://github.com/tajo/ladle",
    "version": 1
  },
  "stories": {
    "storyname--capital-city": {
      "name": "Capital city",
      "levels": [
        "Storyname"
      ],
      "locStart": 15,
      "locEnd": 17,
      "filePath": "tests/fixtures/storyname.stories.tsx",
      "namedExport": "CapitalCity",
      "meta": {}
    },
    "storyname--champs-élysées": {
      "name": "Champs élysées",
      "levels": [
        "Storyname"
      ],
      "locStart": 19,
      "locEnd": 21,
      "filePath": "tests/fixtures/storyname.stories.tsx",
      "namedExport": "CapitalReplaced",
      "meta": {}
    },
    "storyname--doggo": {
      "name": "Doggo",
      "levels": [
        "Storyname"
      ],
      "locStart": 4,
      "locEnd": 9,
      "filePath": "tests/fixtures/storyname.stories.tsx",
      "namedExport": "Cat",
      "meta": {}
    }
  }
}"
`;

exports[`Test multiple stories 1`] = `
{
  "about": {
    "github": "https://github.com/tajo/ladle",
    "homepage": "https://www.ladle.dev",
    "version": 1,
  },
  "stories": {
    "storyname--capital-city": {
      "filePath": "tests/fixtures/storyname.stories.tsx",
      "levels": [
        "Storyname",
      ],
      "locEnd": 17,
      "locStart": 15,
      "meta": {},
      "name": "Capital city",
      "namedExport": "CapitalCity",
    },
    "storyname--champs-élysées": {
      "filePath": "tests/fixtures/storyname.stories.tsx",
      "levels": [
        "Storyname",
      ],
      "locEnd": 21,
      "locStart": 19,
      "meta": {},
      "name": "Champs élysées",
      "namedExport": "CapitalReplaced",
    },
    "storyname--doggo": {
      "filePath": "tests/fixtures/storyname.stories.tsx",
      "levels": [
        "Storyname",
      ],
      "locEnd": 9,
      "locStart": 4,
      "meta": {},
      "name": "Doggo",
      "namedExport": "Cat",
    },
    "title--cat": {
      "filePath": "tests/fixtures/story-meta.stories.tsx",
      "levels": [
        "Title",
      ],
      "locEnd": 16,
      "locStart": 14,
      "meta": {
        "baseweb": {
          "browsers": [
            "chrome",
            "firefox",
          ],
          "theme": "dark",
          "width": "500px",
        },
        "links": true,
      },
      "name": "Cat",
      "namedExport": "Cat",
    },
    "title--dog": {
      "filePath": "tests/fixtures/story-meta.stories.tsx",
      "levels": [
        "Title",
      ],
      "locEnd": 28,
      "locStart": 26,
      "meta": {
        "baseweb": {
          "browsers": [
            "chrome",
            "webkit",
          ],
          "theme": "dark",
        },
      },
      "name": "Dog",
      "namedExport": "Dog",
    },
  },
}
`;

exports[`Turn file name delimiters into spaces and levels correctly 1`] = `
"{
  "about": {
    "homepage": "https://www.ladle.dev",
    "github": "https://github.com/tajo/ladle",
    "version": 1
  },
  "stories": {
    "our-animals--mammals--cat": {
      "name": "Cat",
      "levels": [
        "Our animals",
        "Mammals"
      ],
      "locStart": 4,
      "locEnd": 6,
      "filePath": "tests/fixtures/our-animals--mammals.stories.tsx",
      "namedExport": "Cat",
      "meta": {}
    }
  }
}"
`;
