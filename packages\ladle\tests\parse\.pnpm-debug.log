{"0 debug pnpm:scope": {"selected": 1, "workspacePrefix": "/Users/<USER>/Projects/ladle"}, "1 error pnpm": {"errno": 1, "code": "ELIFECYCLE", "pkgid": "@ladle/react@2.4.2", "stage": "test", "script": "vitest", "pkgname": "@ladle/react", "err": {"name": "pnpm", "message": "@ladle/react@2.4.2 test: `vitest`\nExit status 1", "code": "ELIFECYCLE", "stack": "pnpm: @ladle/react@2.4.2 test: `vitest`\nExit status 1\n    at EventEmitter.<anonymous> (/Users/<USER>/.node/corepack/pnpm/7.3.0/dist/pnpm.cjs:108415:20)\n    at EventEmitter.emit (node:events:527:28)\n    at ChildProcess.<anonymous> (/Users/<USER>/.node/corepack/pnpm/7.3.0/dist/pnpm.cjs:94981:18)\n    at ChildProcess.emit (node:events:527:28)\n    at maybeClose (node:internal/child_process:1092:16)\n    at Process.ChildProcess._handle.onexit (node:internal/child_process:302:5)"}}}