// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Capital letters in story names converted into delimiters 1`] = `
{
  "entry": "tests/fixtures/capitalization.stories.tsx",
  "exportDefaultProps": {
    "meta": undefined,
    "title": undefined,
  },
  "fileId": "capitalization",
  "namedExportToMeta": {},
  "namedExportToStoryName": {},
  "stories": [
    {
      "componentName": "capitalization$$big$barking$dog",
      "locEnd": 10,
      "locStart": 8,
      "namedExport": "BigBarkingDog",
      "storyId": "capitalization--big-barking-dog",
    },
    {
      "componentName": "capitalization$$blue$tiny$cat",
      "locEnd": 6,
      "locStart": 4,
      "namedExport": "BlueTinyCat",
      "storyId": "capitalization--blue-tiny-cat",
    },
  ],
  "storyParams": {},
  "storySource": "import * as React from "react";
import type { Story } from "@ladle/react";

export const BlueTinyCat: Story = () => {
  return <h1>Blue Tiny Cat</h1>;
};

export const BigBarkingDog: Story = () => {
  return <h1> Big Barking Dog</h1>;
};
",
}
`;

exports[`Capital letters in the filename converted into delimiters 1`] = `
{
  "entry": "tests/fixtures/filenameCapitalization.stories.tsx",
  "exportDefaultProps": {
    "meta": undefined,
    "title": undefined,
  },
  "fileId": "filenameCapitalization",
  "namedExportToMeta": {},
  "namedExportToStoryName": {},
  "stories": [
    {
      "componentName": "filename$capitalization$$test",
      "locEnd": 6,
      "locStart": 4,
      "namedExport": "Test",
      "storyId": "filename-capitalization--test",
    },
  ],
  "storyParams": {},
  "storySource": "import * as React from "react";
import type { Story } from "../../lib/app/exports";

export const Test: Story = () => {
  return <h1>Test</h1>;
};
",
}
`;

exports[`Default title is used instead of the file name 1`] = `
{
  "entry": "tests/fixtures/default-title.stories.tsx",
  "exportDefaultProps": {
    "meta": undefined,
    "title": "Title",
  },
  "fileId": "default-title",
  "namedExportToMeta": {},
  "namedExportToStoryName": {},
  "stories": [
    {
      "componentName": "title$$cat",
      "locEnd": 10,
      "locStart": 8,
      "namedExport": "Cat",
      "storyId": "title--cat",
    },
  ],
  "storyParams": {},
  "storySource": "import * as React from "react";
import type { StoryDefault, Story } from "../../lib/app/exports";

export default {
  title: "Title",
} satisfies StoryDefault;

export const Cat: Story = () => {
  return <h1>Cat</h1>;
};
",
}
`;

exports[`Extract and merge story meta 1`] = `
{
  "entry": "tests/fixtures/story-meta.stories.tsx",
  "exportDefaultProps": {
    "meta": {
      "baseweb": {
        "browsers": [
          "chrome",
          "webkit",
        ],
        "theme": "dark",
      },
    },
    "title": "Title",
  },
  "fileId": "story-meta",
  "namedExportToMeta": {
    "Cat": {
      "baseweb": {
        "browsers": [
          "chrome",
          "firefox",
        ],
        "width": "500px",
      },
      "links": true,
    },
  },
  "namedExportToStoryName": {},
  "stories": [
    {
      "componentName": "title$$cat",
      "locEnd": 16,
      "locStart": 14,
      "namedExport": "Cat",
      "storyId": "title--cat",
    },
    {
      "componentName": "title$$dog",
      "locEnd": 28,
      "locStart": 26,
      "namedExport": "Dog",
      "storyId": "title--dog",
    },
  ],
  "storyParams": {
    "title--cat": {
      "meta": {
        "baseweb": {
          "browsers": [
            "chrome",
            "firefox",
          ],
          "theme": "dark",
          "width": "500px",
        },
        "links": true,
      },
      "title": "Title",
    },
    "title--dog": {
      "meta": {
        "baseweb": {
          "browsers": [
            "chrome",
            "webkit",
          ],
          "theme": "dark",
        },
      },
      "title": "Title",
    },
  },
  "storySource": "import * as React from "react";
import type { StoryDefault, Story } from "../../lib/app/exports";

export default {
  title: "Title",
  meta: {
    baseweb: {
      theme: "dark",
      browsers: ["chrome", "webkit"],
    },
  },
} satisfies StoryDefault;

export const Cat: Story = () => {
  return <h1>Cat</h1>;
};

Cat.meta = {
  baseweb: {
    browsers: ["chrome", "firefox"],
    width: "500px",
  },
  links: true,
};

export const Dog: Story = () => {
  return <h1>Dog</h1>;
};
",
}
`;

exports[`Extract default meta 1`] = `
{
  "entry": "tests/fixtures/default-meta.stories.tsx",
  "exportDefaultProps": {
    "meta": {
      "baseweb": {
        "foo": "title",
      },
    },
    "title": "Title",
  },
  "fileId": "default-meta",
  "namedExportToMeta": {},
  "namedExportToStoryName": {},
  "stories": [
    {
      "componentName": "title$$cat",
      "locEnd": 15,
      "locStart": 13,
      "namedExport": "Cat",
      "storyId": "title--cat",
    },
  ],
  "storyParams": {
    "title--cat": {
      "meta": {
        "baseweb": {
          "foo": "title",
        },
      },
      "title": "Title",
    },
  },
  "storySource": "import * as React from "react";
import type { StoryDefault, Story } from "../../lib/app/exports";

export default {
  title: "Title",
  meta: {
    baseweb: {
      foo: "title",
    },
  },
} satisfies StoryDefault;

export const Cat: Story = () => {
  return <h1>Cat</h1>;
};
",
}
`;

exports[`Single file with two stories 1`] = `
{
  "entry": "tests/fixtures/animals.stories.tsx",
  "exportDefaultProps": {
    "meta": undefined,
    "title": undefined,
  },
  "fileId": "animals",
  "namedExportToMeta": {},
  "namedExportToStoryName": {},
  "stories": [
    {
      "componentName": "animals$$cat",
      "locEnd": 6,
      "locStart": 4,
      "namedExport": "Cat",
      "storyId": "animals--cat",
    },
    {
      "componentName": "animals$$dog",
      "locEnd": 10,
      "locStart": 8,
      "namedExport": "Dog",
      "storyId": "animals--dog",
    },
  ],
  "storyParams": {},
  "storySource": "import * as React from "react";
import type { Story } from "@ladle/react";

export const Cat: Story = () => {
  return <h1>Cat</h1>;
};

export const Dog: Story = () => {
  return <h1>Dog</h1>;
};
",
}
`;

exports[`Story name replaces named export as a story name 1`] = `
{
  "entry": "tests/fixtures/storyname.stories.tsx",
  "exportDefaultProps": {
    "meta": undefined,
    "title": undefined,
  },
  "fileId": "storyname",
  "namedExportToMeta": {},
  "namedExportToStoryName": {
    "CapitalReplaced": "Champs Élysées",
    "Cat": "Doggo",
  },
  "stories": [
    {
      "componentName": "storyname$$capital$city",
      "locEnd": 17,
      "locStart": 15,
      "namedExport": "CapitalCity",
      "storyId": "storyname--capital-city",
    },
    {
      "componentName": "storyname$$champs$élysées",
      "locEnd": 21,
      "locStart": 19,
      "namedExport": "CapitalReplaced",
      "storyId": "storyname--champs-élysées",
    },
    {
      "componentName": "storyname$$doggo",
      "locEnd": 9,
      "locStart": 4,
      "namedExport": "Cat",
      "storyId": "storyname--doggo",
    },
  ],
  "storyParams": {},
  "storySource": "import * as React from "react";
import type { Story } from "../../lib/app/exports";

export const Cat: Story = () => {
  const Stop = { storyName: "" };
  // should be ignored
  Stop.storyName = "What";
  return <h1>Cat</h1>;
};

Cat.storyName = "Doggo";
// @ts-expect-error
Cat.foo = "Ha";

export const CapitalCity: Story = () => {
  return <h1>DC</h1>;
};

export const CapitalReplaced: Story = () => {
  return <h1>CapitalReplaced</h1>;
};
CapitalReplaced.storyName = "Champs Élysées";
",
}
`;

exports[`Turn file name delimiters into spaces and levels correctly 1`] = `
{
  "entry": "tests/fixtures/our-animals--mammals.stories.tsx",
  "exportDefaultProps": {
    "meta": undefined,
    "title": undefined,
  },
  "fileId": "our-animals--mammals",
  "namedExportToMeta": {},
  "namedExportToStoryName": {},
  "stories": [
    {
      "componentName": "our$animals$$mammals$$cat",
      "locEnd": 6,
      "locStart": 4,
      "namedExport": "Cat",
      "storyId": "our-animals--mammals--cat",
    },
  ],
  "storyParams": {},
  "storySource": "import * as React from "react";
import type { Story } from "../../lib/app/exports";

export const Cat: Story = () => {
  return <h1>Cat</h1>;
};
",
}
`;
