# Change Log

## 0.5.0

### Minor Changes

- [#587](https://github.com/tajo/ladle/pull/587) [`1178c37`](https://github.com/tajo/ladle/commit/1178c37bfeb5aba0a3249811151f463beacdecef) Thanks [@Wesleyzxc](https://github.com/Wesleyzxc)! - add expandStoryTree config option

## 0.4.0

### Minor Changes

- [#558](https://github.com/tajo/ladle/pull/558) [`55de74a`](https://github.com/tajo/ladle/commit/55de74a1c2dcedd7f50b088954afe0b83c69a4d6) Thanks [@park-g](https://github.com/park-g)! - Add option to set HMR host and port via the ladle config and update the docs with these options.

## 0.3.7

### Patch Changes

- [#492](https://github.com/tajo/ladle/pull/492) [`ba388db`](https://github.com/tajo/ladle/commit/ba388db19f2e07000b6fae95c6949a5750ee0b7d) Thanks [@tajo](https://github.com/tajo)! - Document MDX and mock date.

## 0.3.6

### Patch Changes

- [#457](https://github.com/tajo/ladle/pull/457) [`a53b222`](https://github.com/tajo/ladle/commit/a53b222c6c582c78802829dba7f4026b46b99503) Thanks [@matthewwolfe](https://github.com/matthewwolfe)! - fixed link to contributing guide and updated guide to reflect correct node and pnpm versions

## 0.3.5

### Patch Changes

- [#374](https://github.com/tajo/ladle/pull/374) [`9ff28f9`](https://github.com/tajo/ladle/commit/9ff28f9a3e0774229de81d62facd15314e882faf) Thanks [@tajo](https://github.com/tajo)! - Add docs about next/image and typescript

## 0.3.4

### Patch Changes

- [#217](https://github.com/tajo/ladle/pull/217) [`bc6ad85`](https://github.com/tajo/ladle/commit/bc6ad85efef15676cb1bcc2e86dc48b2dcea02e1) Thanks [@tajo](https://github.com/tajo)! - Bump all dependencies to the latest versions.

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## 0.3.3

### Patch Changes

- [#138](https://github.com/tajo/ladle/pull/138) [`e2069f4`](https://github.com/tajo/ladle/commit/e2069f4b3d2b0b69a034c28e508a171ce011c45a) Thanks [@tajo](https://github.com/tajo)! - Add a section explaining the limitations of story syntax - some parts need to be static so they can be parsed.

* [#126](https://github.com/tajo/ladle/pull/126) [`0a44aa2`](https://github.com/tajo/ladle/commit/0a44aa2a1b40f392db3163cddfdae7633771edec) Thanks [@beckend](https://github.com/beckend)! - feat: allow configuration of the whole Vite config.resolve object

## [0.3.2](https://github.com/tajo/ladle/compare/<EMAIL>@0.3.2) (2022-04-29)

**Note:** Version bump only for package website

## [0.3.1](https://github.com/tajo/ladle/compare/<EMAIL>@0.3.1) (2022-04-28)

**Note:** Version bump only for package website

# [0.3.0](https://github.com/tajo/ladle/compare/<EMAIL>@0.3.0) (2022-04-28)

### Features

- add config babelParserOpts ([#111](https://github.com/tajo/ladle/issues/111)) ([2102752](https://github.com/tajo/ladle/commit/2102752e50a41606551d31c416a5fed69424312f))

## [0.2.1](https://github.com/tajo/ladle/compare/<EMAIL>@0.2.1) (2022-04-11)

**Note:** Version bump only for package website

# [0.2.0](https://github.com/tajo/ladle/compare/<EMAIL>@0.2.0) (2022-03-30)

### Features

- add support for components.ts and components.jsx in getComponents util ([#84](https://github.com/tajo/ladle/issues/84)) ([c0320af](https://github.com/tajo/ladle/commit/c0320af698083006bd4ec29db6be3b066020c2b3))
- support publicDir from vite ([#72](https://github.com/tajo/ladle/issues/72)) ([088c321](https://github.com/tajo/ladle/commit/088c3217a87e57cb32f2bc1d534716eff2960c09))

## [0.1.6](https://github.com/tajo/ladle/compare/<EMAIL>@0.1.6) (2022-03-16)

**Note:** Version bump only for package website

## [0.1.5](https://github.com/tajo/ladle/compare/<EMAIL>@0.1.5) (2022-03-16)

**Note:** Version bump only for package website

## [0.1.4](https://github.com/tajo/ladle/compare/<EMAIL>@0.1.4) (2022-03-15)

**Note:** Version bump only for package website

## [0.1.3](https://github.com/tajo/ladle/compare/<EMAIL>@0.1.3) (2022-03-15)

**Note:** Version bump only for package website

## [0.1.2](https://github.com/tajo/ladle/compare/<EMAIL>@0.1.2) (2022-03-13)

**Note:** Version bump only for package website

## [0.1.1](https://github.com/tajo/ladle/compare/<EMAIL>@0.1.1) (2022-03-09)

**Note:** Version bump only for package website

# [0.1.0](https://github.com/tajo/ladle/compare/<EMAIL>@0.1.0) (2022-01-23)

### Features

- add support for controls with args / argTypes ([#38](https://github.com/tajo/ladle/issues/38)) ([f8ec667](https://github.com/tajo/ladle/commit/f8ec6679fe7fcd508ca445dbca449549920caba8))

## [0.0.2](https://github.com/tajo/ladle/compare/<EMAIL>@0.0.2) (2021-10-24)

**Note:** Version bump only for package website

## 0.0.1 (2021-03-04)

**Note:** Version bump only for package website
