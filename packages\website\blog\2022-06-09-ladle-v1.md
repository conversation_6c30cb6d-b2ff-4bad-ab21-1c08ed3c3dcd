---
slug: ladle-v1
title: Ladle v1
authors: vojtech
tags: [ladle, storybook, react, testing, components]
---

<PERSON><PERSON> has been out for 3 months and the community feedback was overwhelming and amazing. Thank you! Some numbers:

- 🎯 20,000 unique visitors on this website.
- ⭐ 1,300+ GitHub stars.
- 🖊️ 10 contributors.
- 💬 100+ folks joined our [Discord](https://discord.gg/H6FSHjyW7e) community.

Today, it's time to graduate <PERSON><PERSON> to its first major stable version 1. We've fixed many bugs discovered by early adopters and added some big features as well:

- [Addon to test accessibility](https://github.com/tajo/ladle/pull/115)
- [Addon to show story source](https://github.com/tajo/ladle/pull/112)

<!-- truncate -->

V1 brings the most requested feature - **the full access to Vite configuration**. When Ladle was released, it was intended as a tool that could replace Storybook. The bundler (Vite) was just an implementation detail and completely hidden away. However, for more advanced setups, you need to customize various aspects of compilation (like Storybook's Webpack). It makes sense to fully expose [`vite.config.js`](https://vitejs.dev/config/) and embrace Vite first applications as well. Ladle's configuration has been rewritten from the ground up.

Check the new [config documentation](/docs/config).

## Breaking Changes

- Ladle now loads top-level `vite.config.js/ts/mjs` and uses all its options.
- All Vite related options from `config.mjs` are removed and an error will be thrown, use `vite.config.js` instead.
- `enableFlow` option removed, you can create your own plugin (check our e2e/flow test).
- Programmatic API imports changed to `@ladle/react/serve` and `@ladle/react/build`.
- `--out` renamed to `--outDir` to mimic Vite configuration, added `-o` alias, `outDir` moved to the top-level in `config.mjs`.
- `--port` has an alias `-p`, `port` moved to the top-level in `config.mjs`.
- `vite.config.js` can be customized through `viteConfig` and `--viteConfig`.
- `--base-url` removed, use `base` in `vite.config.js`.
- `--open` removed, use `server.open` in `vite.config.js`.
- `--sourcemap` removed, use `build.sourcemap` in `vite.config.js`.

This makes our internal setup much simpler since we don't need to pass through individual Vite settings - you can now customize all of them without Ladle being in your way.

## Next big features?

We are adding MDX support and making it easier to use Ladle for documentation.
