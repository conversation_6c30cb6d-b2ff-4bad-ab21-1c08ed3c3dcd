---
id: a11y
title: Accessibility
---

import Image from "@theme/IdealImage";
import imgA11y from "../static/img/a11y.png";

Ladle is accessible. If you want to build accessible component, the wrapping environment should be accessible and keyboard friendly too. That's why your story comes first and the side navigation / addons second.

Ladle also includes an a11y testing through [axe-core](https://github.com/dequelabs/axe-core), so you can fix any a11y violations in your components.

You have to enable it in your `.ladle/config.mjs`:

```js
/** @type {import('@ladle/react').UserConfig} */
export default {
  addons: {
    a11y: {
      enabled: true,
    },
  },
};
```

<Image img={imgA11y} alt="A11y addon" />
