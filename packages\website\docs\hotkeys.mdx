---
id: hotkeys
title: Hotkeys
---

import Image from "@theme/IdealImage";
import imgHotkeys from "../static/img/hotkeys.png";

Ladle has a few hotkeys to make your life easier:

- `/` or `⌘ cmd ＋ p` - Focus search input in the sidebar
- `⌥ opt ＋ →` - Go to the next story
- `⌥ opt ＋ ←` - Go to the previous story
- `⌥ opt ＋ ↓` - Go to the next component
- `⌥ opt ＋ ↑` - Go to the previous component
- `c` - Toggle controls addon
- `d` - Toggle dark mode
- `f` - Toggle fullscreen mode
- `w` - Toggle width addon
- `r` - Toggle right-to-left mode
- `s` - Toggle story source addon
- `a` - Toggle accessibility addon

## Settings

These defaults can be customized through the [configuration](config#hotkeys).

Some stories might have utilize their own set of hotkeys. If you want to prevent conflicts with <PERSON><PERSON>, you can disable all Ladle shortcuts for a specific story by using the `meta` parameter:

```tsx
export default {
  meta: {
    hotkeys: false,
  },
};
Story.meta = {
  hotkeys: false,
};
```

## Cheat Sheet

If you need a quick reminder of all the hotkeys, you can open the about addon:

<Image img={imgHotkeys} alt="Hotkeys cheatsheet" />
