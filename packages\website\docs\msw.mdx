---
id: msw
title: MSW
---

<PERSON><PERSON> has a build-in support for [MSW v2](https://mswjs.io/). It allows API mocking by intercepting requests on the network level.

You just have to enable it in your `.ladle/config.mjs`:

```js
/** @type {import('@ladle/react').UserConfig} */
export default {
  addons: {
    msw: {
      enabled: true,
    },
  },
};
```

Ladle automatically sets up the MSW service worker for you - copying it over into the public folder. <PERSON><PERSON> also re-exports MSW library so you don't have to install it yourself.

## Usage

```tsx
import type { Story } from "@ladle/react";
import { msw } from "@ladle/react";
import { useEffect, useState } from "react";

const FETCH_URL = "/api-url.json";

// Set default handlers for all stories
export default {
  msw: [
    msw.http.get(FETCH_URL, () => {
      return msw.HttpResponse.json([{ id: 1, title: "msw post default" }]);
    }),
  ],
};

export const Mocked: Story = () => {
  const [posts, setPosts] = useState([]);
  useEffect(() => {
    fetchData();
    async function fetchData() {
      try {
        const data = await fetch(FETCH_URL);
        const json = await data.json();
        setPosts(json);
      } catch (e) {
        console.error(e);
      }
    };
  }, []);
  return (
    <>
      <h1>Posts</h1>
      <ul>
        {posts.map((post: { id: number; title: string }) => (
          <li key={post.id}>{post.title}</li>
        ))}
      </ul>
    </>
  );
};

// handlers for this story only
// Mocked.msw = [];
```

You can also use MSW to mock GraphQL requests. Check their [documentation](https://mswjs.io/docs/).

## Import msw/node

When you need to setup node environment for MSW, you would use:

```js
import { setupServer } from 'msw/node'
```

You can use the Ladle's version of MSW by using this import instead:

```js
import { setupServer } from "@ladle/react/msw-node";
```

