/* stylelint-disable docusaurus/copyright-header */
/**
 * Any CSS included here will be global. The classic template
 * bundles Infima by default. Infima is a CSS framework designed to
 * work well for content-centric websites.
 */

/* You can override the default Infima variables here. */
:root {
  --ifm-color-primary: #174291;
  --ifm-color-primary-dark: rgb(33, 175, 144);
  --ifm-color-primary-darker: rgb(31, 165, 136);
  --ifm-color-primary-darkest: rgb(26, 136, 112);
  --ifm-color-primary-light: rgb(70, 203, 174);
  --ifm-color-primary-lighter: rgb(102, 212, 189);
  --ifm-color-primary-lightest: rgb(146, 224, 208);
  --ifm-code-font-size: 95%;
}

.docusaurus-highlight-code-line {
  background-color: rgb(72, 77, 91);
  display: block;
  margin: 0 calc(-1 * var(--ifm-pre-padding));
  padding: 0 var(--ifm-pre-padding);
}

.hero .button {
  color: #fff !important;
}

.hero .button:hover {
  color: #000 !important;
}

[data-theme="dark"] .hero {
  color: #fff;
}

[data-theme="dark"] a {
  color: #58a6ff;
}

[data-theme="dark"] a:hover {
  color: #58a6ff;
}
.navbar__logo {
  margin-right: 32px;
}

[data-theme="dark"] .navbar__logo > img {
  filter: invert(1);
}

[data-theme="dark"] .navbar__brand > img {
  filter: invert(1);
}

.main-logo {
  width: 75px;
  filter: invert(1);
  margin-right: 12px;
}

.features_src-pages-styles-module .container {
  padding: 1rem 3rem;
}

.features_src-pages-styles-module h3 {
  margin-top: 2rem;
}
