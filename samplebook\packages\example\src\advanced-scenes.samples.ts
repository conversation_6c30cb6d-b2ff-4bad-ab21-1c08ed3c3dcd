import * as THREE from 'three';

function createScene(width = 400, height = 300) {
  const scene = new THREE.Scene();
  const camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000);
  const renderer = new THREE.WebGLRenderer({ antialias: true });
  
  renderer.setSize(width, height);
  renderer.setClearColor(0x000011);
  
  camera.position.z = 5;
  
  return { scene, camera, renderer };
}

export function particleSystem() {
  const { scene, camera, renderer } = createScene();
  
  // Create particle system
  const particleCount = 1000;
  const particles = new THREE.BufferGeometry();
  const positions = new Float32Array(particleCount * 3);
  
  for (let i = 0; i < particleCount * 3; i++) {
    positions[i] = (Math.random() - 0.5) * 10;
  }
  
  particles.setAttribute('position', new THREE.BufferAttribute(positions, 3));
  
  const material = new THREE.PointsMaterial({
    color: 0xffffff,
    size: 0.1
  });
  
  const particleSystem = new THREE.Points(particles, material);
  scene.add(particleSystem);
  
  function animate() {
    particleSystem.rotation.y += 0.01;
    renderer.render(scene, camera);
    requestAnimationFrame(animate);
  }
  animate();
  
  return renderer.domElement;
}

export function colorfulTorus() {
  const { scene, camera, renderer } = createScene();
  
  // Add lighting
  const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
  scene.add(ambientLight);
  
  const pointLight = new THREE.PointLight(0xffffff, 1, 100);
  pointLight.position.set(10, 10, 10);
  scene.add(pointLight);
  
  // Create torus with rainbow material
  const geometry = new THREE.TorusGeometry(1, 0.4, 16, 100);
  const material = new THREE.MeshPhongMaterial({
    color: 0xff6347,
    shininess: 100
  });
  
  const torus = new THREE.Mesh(geometry, material);
  scene.add(torus);
  
  let time = 0;
  function animate() {
    time += 0.01;
    
    // Change color over time
    const hue = (time * 0.1) % 1;
    material.color.setHSL(hue, 0.8, 0.6);
    
    torus.rotation.x += 0.01;
    torus.rotation.y += 0.02;
    
    renderer.render(scene, camera);
    requestAnimationFrame(animate);
  }
  animate();
  
  return renderer.domElement;
}

export function geometryMorph() {
  const { scene, camera, renderer } = createScene();
  
  // Add lighting
  const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
  scene.add(ambientLight);
  
  const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
  directionalLight.position.set(1, 1, 1);
  scene.add(directionalLight);
  
  // Create morphing geometry
  const geometry = new THREE.SphereGeometry(1, 32, 32);
  const material = new THREE.MeshLambertMaterial({ color: 0x00ffaa });
  const mesh = new THREE.Mesh(geometry, material);
  
  scene.add(mesh);
  
  let time = 0;
  function animate() {
    time += 0.02;
    
    // Morph the sphere by modifying vertices
    const positions = geometry.attributes.position.array as Float32Array;
    for (let i = 0; i < positions.length; i += 3) {
      const x = positions[i];
      const y = positions[i + 1];
      const z = positions[i + 2];
      
      const distance = Math.sqrt(x * x + y * y + z * z);
      const wave = Math.sin(distance * 5 + time) * 0.1;
      
      positions[i] = x * (1 + wave);
      positions[i + 1] = y * (1 + wave);
      positions[i + 2] = z * (1 + wave);
    }
    
    geometry.attributes.position.needsUpdate = true;
    geometry.computeVertexNormals();
    
    mesh.rotation.y += 0.01;
    
    renderer.render(scene, camera);
    requestAnimationFrame(animate);
  }
  animate();
  
  return renderer.domElement;
}
