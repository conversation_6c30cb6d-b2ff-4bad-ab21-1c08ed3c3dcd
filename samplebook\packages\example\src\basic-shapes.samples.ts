import * as THREE from 'three';

// Helper function to create a basic scene setup
function createScene(width = 400, height = 300) {
  const scene = new THREE.Scene();
  const camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000);
  const renderer = new THREE.WebGLRenderer({ antialias: true });
  
  renderer.setSize(width, height);
  renderer.setClearColor(0xf0f0f0);
  
  // Add some basic lighting
  const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
  scene.add(ambientLight);
  
  const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
  directionalLight.position.set(1, 1, 1);
  scene.add(directionalLight);
  
  camera.position.z = 5;
  
  return { scene, camera, renderer };
}

// Helper function to start animation loop
function animate(renderer: THREE.WebGLRenderer, scene: THREE.Scene, camera: THREE.Camera, mesh?: THREE.Mesh) {
  function render() {
    if (mesh) {
      mesh.rotation.x += 0.01;
      mesh.rotation.y += 0.01;
    }
    renderer.render(scene, camera);
    requestAnimationFrame(render);
  }
  render();
}

export function basicCube() {
  const { scene, camera, renderer } = createScene();
  
  // Create a cube
  const geometry = new THREE.BoxGeometry();
  const material = new THREE.MeshLambertMaterial({ color: 0x00ff00 });
  const cube = new THREE.Mesh(geometry, material);
  
  scene.add(cube);
  
  // Start animation
  animate(renderer, scene, camera, cube);
  
  return renderer.domElement;
}

export function redSphere() {
  const { scene, camera, renderer } = createScene();
  
  // Create a sphere
  const geometry = new THREE.SphereGeometry(1, 32, 32);
  const material = new THREE.MeshLambertMaterial({ color: 0xff0000 });
  const sphere = new THREE.Mesh(geometry, material);
  
  scene.add(sphere);
  
  // Start animation
  animate(renderer, scene, camera, sphere);
  
  return renderer.domElement;
}

export function blueCylinder() {
  const { scene, camera, renderer } = createScene();
  
  // Create a cylinder
  const geometry = new THREE.CylinderGeometry(0.5, 0.5, 2, 32);
  const material = new THREE.MeshLambertMaterial({ color: 0x0000ff });
  const cylinder = new THREE.Mesh(geometry, material);
  
  scene.add(cylinder);
  
  // Start animation
  animate(renderer, scene, camera, cylinder);
  
  return renderer.domElement;
}

export function wireframeTetrahedron() {
  const { scene, camera, renderer } = createScene();
  
  // Create a tetrahedron
  const geometry = new THREE.TetrahedronGeometry(1.5);
  const material = new THREE.MeshBasicMaterial({ 
    color: 0xff00ff, 
    wireframe: true 
  });
  const tetrahedron = new THREE.Mesh(geometry, material);
  
  scene.add(tetrahedron);
  
  // Start animation
  animate(renderer, scene, camera, tetrahedron);
  
  return renderer.domElement;
}
