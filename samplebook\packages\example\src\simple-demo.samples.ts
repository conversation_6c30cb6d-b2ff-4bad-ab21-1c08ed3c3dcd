// Simple non-threejs samples to show Samplebook works with any DOM content

export function helloWorld() {
  const div = document.createElement('div');
  div.style.padding = '40px';
  div.style.textAlign = 'center';
  div.style.fontSize = '24px';
  div.style.color = '#333';
  div.style.backgroundColor = '#f0f8ff';
  div.style.border = '2px solid #4169e1';
  div.style.borderRadius = '8px';
  div.textContent = 'Hello from Samplebook! 👋';
  return div;
}

export function animatedButton() {
  const container = document.createElement('div');
  container.style.padding = '40px';
  container.style.textAlign = 'center';
  
  const button = document.createElement('button');
  button.textContent = 'Click me!';
  button.style.padding = '12px 24px';
  button.style.fontSize = '16px';
  button.style.backgroundColor = '#4caf50';
  button.style.color = 'white';
  button.style.border = 'none';
  button.style.borderRadius = '4px';
  button.style.cursor = 'pointer';
  button.style.transition = 'all 0.3s ease';
  
  let clickCount = 0;
  button.addEventListener('click', () => {
    clickCount++;
    button.textContent = `Clicked ${clickCount} times!`;
    button.style.backgroundColor = `hsl(${clickCount * 30}, 70%, 50%)`;
  });
  
  button.addEventListener('mouseenter', () => {
    button.style.transform = 'scale(1.1)';
  });
  
  button.addEventListener('mouseleave', () => {
    button.style.transform = 'scale(1)';
  });
  
  container.appendChild(button);
  return container;
}

export function colorfulCanvas() {
  const canvas = document.createElement('canvas');
  canvas.width = 400;
  canvas.height = 300;
  canvas.style.border = '1px solid #ccc';
  
  const ctx = canvas.getContext('2d')!;
  
  function drawRandomCircles() {
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    for (let i = 0; i < 20; i++) {
      const x = Math.random() * canvas.width;
      const y = Math.random() * canvas.height;
      const radius = Math.random() * 30 + 10;
      const hue = Math.random() * 360;
      
      ctx.beginPath();
      ctx.arc(x, y, radius, 0, Math.PI * 2);
      ctx.fillStyle = `hsl(${hue}, 70%, 60%)`;
      ctx.fill();
    }
  }
  
  // Initial draw
  drawRandomCircles();
  
  // Redraw every 2 seconds
  setInterval(drawRandomCircles, 2000);
  
  return canvas;
}
