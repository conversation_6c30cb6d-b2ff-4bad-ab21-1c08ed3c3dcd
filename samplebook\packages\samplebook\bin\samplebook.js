#!/usr/bin/env node

import { fileURLToPath, pathToFileURL } from 'url';
import { dirname, join } from 'path';
import { readFileSync, existsSync } from 'fs';
import { spawn } from 'child_process';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// In development, run from TypeScript source using tsx
// In production, run from compiled JavaScript
const libEntryPoint = join(__dirname, '..', 'lib', 'cli', 'index.js');
const srcEntryPoint = join(__dirname, '..', 'src', 'cli', 'index.ts');

if (existsSync(libEntryPoint)) {
  // Production: use compiled JS
  const entryURL = pathToFileURL(libEntryPoint).href;
  import(entryURL).catch(err => {
    console.error('Failed to start Samplebook:', err);
    process.exit(1);
  });
} else {
  // Development: use tsx to run TypeScript
  const tsxPath = join(__dirname, '..', 'node_modules', '.bin', 'tsx');
  const child = spawn(process.platform === 'win32' ? 'tsx.cmd' : 'tsx', [srcEntryPoint, ...process.argv.slice(2)], {
    stdio: 'inherit',
    cwd: process.cwd()
  });

  child.on('exit', (code) => {
    process.exit(code || 0);
  });
}
