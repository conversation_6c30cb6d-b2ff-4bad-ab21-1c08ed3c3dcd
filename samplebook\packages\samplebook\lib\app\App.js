import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { samples, errorMessage } from 'virtual:samplebook-samples';
import Sidebar from './components/Sidebar';
import SampleRenderer from './components/SampleRenderer';
import './App.css';
const App = () => {
    const [selectedSample, setSelectedSample] = useState(null);
    const [sampleList, setSampleList] = useState({});
    useEffect(() => {
        setSampleList(samples);
        // Auto-select first sample if available
        const sampleKeys = Object.keys(samples);
        if (sampleKeys.length > 0 && !selectedSample) {
            setSelectedSample(sampleKeys[0]);
        }
    }, [selectedSample]);
    if (errorMessage) {
        return (_jsxs("div", { className: "error-container", children: [_jsx("h1", { children: "Error Loading <PERSON>" }), _jsx("pre", { children: errorMessage })] }));
    }
    const sampleKeys = Object.keys(sampleList);
    if (sampleKeys.length === 0) {
        return (_jsxs("div", { className: "empty-container", children: [_jsx("h1", { children: "No Samples Found" }), _jsx("p", { children: "Create some .samples.ts files in your project to get started!" }), _jsxs("div", { className: "example", children: [_jsx("h3", { children: "Example:" }), _jsx("pre", { children: `// example.samples.ts
export function basicCube() {
  const div = document.createElement('div');
  div.textContent = 'Hello from a sample!';
  return div;
}` })] })] }));
    }
    return (_jsxs("div", { className: "app", children: [_jsx(Sidebar, { samples: sampleList, selectedSample: selectedSample, onSelectSample: setSelectedSample }), _jsx("main", { className: "main-content", children: selectedSample && sampleList[selectedSample] ? (_jsx(SampleRenderer, { sample: sampleList[selectedSample], sampleId: selectedSample })) : (_jsx("div", { className: "no-selection", children: _jsx("h2", { children: "Select a sample to view" }) })) })] }));
};
export default App;
