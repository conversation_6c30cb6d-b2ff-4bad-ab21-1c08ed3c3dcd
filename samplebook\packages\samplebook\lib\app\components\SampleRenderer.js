import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import { useEffect, useRef, useState } from 'react';
const SampleRenderer = ({ sample, sampleId }) => {
    const containerRef = useRef(null);
    const [error, setError] = useState(null);
    const [isLoading, setIsLoading] = useState(true);
    useEffect(() => {
        if (!containerRef.current)
            return;
        const container = containerRef.current;
        setError(null);
        setIsLoading(true);
        // Clear previous content
        container.innerHTML = '';
        try {
            // Call the sample function to get the DOM element
            const element = sample.component();
            if (!element || !(element instanceof HTMLElement)) {
                throw new Error('Sample function must return an HTMLElement');
            }
            // Append the element to our container
            container.appendChild(element);
            setIsLoading(false);
        }
        catch (err) {
            console.error('Error rendering sample:', err);
            setError(err instanceof Error ? err.message : 'Unknown error occurred');
            setIsLoading(false);
        }
        // Cleanup function
        return () => {
            if (container) {
                container.innerHTML = '';
            }
        };
    }, [sample, sampleId]);
    return (_jsxs(_Fragment, { children: [_jsxs("div", { className: "sample-header", children: [_jsx("h2", { children: sample.title }), _jsxs("div", { className: "meta", children: [_jsx("span", { children: sample.file }), " \u2192 ", _jsx("span", { children: sample.export })] })] }), _jsx("div", { className: "sample-container", children: _jsxs("div", { className: "sample-viewport", children: [isLoading && (_jsx("div", { style: {
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                height: '100%',
                                color: '#666'
                            }, children: "Loading sample..." })), error && (_jsxs("div", { style: {
                                padding: '20px',
                                color: '#d32f2f',
                                backgroundColor: '#ffebee',
                                border: '1px solid #ffcdd2',
                                borderRadius: '4px',
                                margin: '20px'
                            }, children: [_jsx("h3", { children: "Error rendering sample:" }), _jsx("pre", { children: error })] })), _jsx("div", { ref: containerRef, className: "sample-content", style: { display: error ? 'none' : 'block' } })] }) })] }));
};
export default SampleRenderer;
