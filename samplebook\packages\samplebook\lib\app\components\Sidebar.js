import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
const Sidebar = ({ samples, selectedSample, onSelectSample }) => {
    const sampleEntries = Object.entries(samples);
    // Group samples by file
    const groupedSamples = sampleEntries.reduce((groups, [id, sample]) => {
        const file = sample.file;
        if (!groups[file]) {
            groups[file] = [];
        }
        groups[file].push({ id, sample });
        return groups;
    }, {});
    return (_jsxs("div", { className: "sidebar", children: [_jsxs("div", { className: "sidebar-header", children: [_jsx("h1", { children: "\uD83D\uDD2C Samplebook" }), _jsxs("p", { children: [sampleEntries.length, " samples found"] })] }), _jsx("ul", { className: "sample-list", children: Object.entries(groupedSamples).map(([file, fileSamples]) => (_jsxs("li", { className: "file-group", children: [_jsx("div", { className: "file-header", children: _jsx("strong", { children: file }) }), fileSamples.map(({ id, sample }) => (_jsx("li", { className: "sample-item", children: _jsxs("button", { className: `sample-button ${selectedSample === id ? 'active' : ''}`, onClick: () => onSelectSample(id), children: [_jsx("div", { className: "sample-title", children: sample.title }), _jsx("div", { className: "sample-meta", children: sample.export })] }) }, id)))] }, file))) })] }));
};
export default Sidebar;
