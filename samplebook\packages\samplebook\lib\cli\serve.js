import { resolve, join, dirname } from 'path';
import { createServer } from 'vite';
import react from '@vitejs/plugin-react';
import { globby } from 'globby';
import getPort from 'get-port';
import boxen from 'boxen';
import { fileURLToPath } from 'url';
import samplebookPlugin from './vite-plugin.js';
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const serve = async (hostRoot, options = { hostRoot }) => {
    console.log('🔬 Starting Samplebook...');
    const resolvedHostRoot = resolve(process.cwd(), hostRoot);
    const port = await getPort({ port: parseInt(options.port || '3000') });
    console.log(`📁 Host project: ${resolvedHostRoot}`);
    console.log(`🔍 Sample pattern: ${options.samples}`);
    // Discover sample files
    const sampleFiles = await globby([options.samples || '**/*.samples.{ts,js}'], {
        cwd: resolvedHostRoot,
        absolute: false
    });
    console.log(`📋 Found ${sampleFiles.length} sample files:`);
    sampleFiles.forEach(file => console.log(`   ${file}`));
    if (sampleFiles.length === 0) {
        console.warn('⚠️  No sample files found. Make sure you have files matching the pattern.');
    }
    try {
        // Get the path to the samplebook app source
        const samplebookAppPath = join(__dirname, '..', 'app');
        const server = await createServer({
            mode: options.mode || 'development',
            root: resolvedHostRoot,
            plugins: [
                samplebookPlugin({
                    hostRoot: resolvedHostRoot,
                    samples: options.samples || '**/*.samples.{ts,js}',
                    appPath: samplebookAppPath
                }),
                react()
            ],
            server: {
                host: options.host || 'localhost',
                port,
                open: true
            },
            resolve: {
                alias: {
                    '@samplebook/app': samplebookAppPath
                }
            },
            optimizeDeps: {
                include: ['react', 'react-dom']
            }
        });
        await server.listen();
        const url = `http://${options.host || 'localhost'}:${port}`;
        console.log(boxen(`🚀 Samplebook is running!\n\n` +
            `Local:   ${url}\n` +
            `Host:    ${resolvedHostRoot}`, {
            padding: 1,
            borderColor: 'green',
            borderStyle: 'round'
        }));
    }
    catch (error) {
        console.error('❌ Failed to start server:', error);
        process.exit(1);
    }
};
export default serve;
