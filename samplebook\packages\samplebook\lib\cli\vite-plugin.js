import { globby } from 'globby';
import { readFileSync } from 'fs';
import { join } from 'path';
import { parse } from '@babel/parser';
import traverse from '@babel/traverse';
import * as t from '@babel/types';
function samplebookPlugin(options) {
    const virtualModuleId = 'virtual:samplebook-samples';
    const resolvedVirtualModuleId = '\0' + virtualModuleId;
    return {
        name: 'samplebook:core',
        resolveId(id) {
            if (id === virtualModuleId) {
                return resolvedVirtualModuleId;
            }
            return null;
        },
        async load(id) {
            if (id === resolvedVirtualModuleId) {
                try {
                    // Discover sample files
                    const sampleFiles = await globby([options.samples], {
                        cwd: options.hostRoot,
                        absolute: false
                    });
                    // Parse each file to extract exports
                    const samples = [];
                    for (const file of sampleFiles) {
                        const filePath = join(options.hostRoot, file);
                        const content = readFileSync(filePath, 'utf8');
                        // Parse with Babel to extract exports
                        const ast = parse(content, {
                            sourceType: 'module',
                            plugins: ['typescript', 'jsx']
                        });
                        const exports = [];
                        traverse(ast, {
                            ExportNamedDeclaration(path) {
                                if (path.node.declaration) {
                                    if (t.isFunctionDeclaration(path.node.declaration) && path.node.declaration.id) {
                                        exports.push(path.node.declaration.id.name);
                                    }
                                    else if (t.isVariableDeclaration(path.node.declaration)) {
                                        path.node.declaration.declarations.forEach(decl => {
                                            if (t.isIdentifier(decl.id)) {
                                                exports.push(decl.id.name);
                                            }
                                        });
                                    }
                                }
                                else if (path.node.specifiers) {
                                    path.node.specifiers.forEach(spec => {
                                        if (t.isExportSpecifier(spec) && t.isIdentifier(spec.exported)) {
                                            exports.push(spec.exported.name);
                                        }
                                    });
                                }
                            }
                        });
                        if (exports.length > 0) {
                            samples.push({ file, exports });
                        }
                    }
                    // Generate the virtual module content
                    return generateVirtualModule(samples, options.hostRoot);
                }
                catch (error) {
                    console.error('Error generating sample list:', error);
                    return `
            export const samples = {};
            export const errorMessage = ${JSON.stringify(error.message)};
          `;
                }
            }
            return null;
        },
        configureServer(server) {
            // Add the Samplebook app HTML
            server.middlewares.use('/', (req, res, next) => {
                if (req.url === '/' || req.url === '/index.html') {
                    res.setHeader('Content-Type', 'text/html');
                    res.end(getAppHTML(options.appPath));
                    return;
                }
                next();
            });
        }
    };
}
function generateVirtualModule(samples, hostRoot) {
    let imports = '';
    let sampleEntries = '';
    samples.forEach(({ file, exports }) => {
        const relativePath = './' + file.replace(/\\/g, '/');
        const fileId = file.replace(/[^a-zA-Z0-9]/g, '_');
        exports.forEach(exportName => {
            const componentId = `${fileId}_${exportName}`;
            imports += `import { ${exportName} as ${componentId} } from '${relativePath}';\n`;
            const sampleId = `${file.replace(/\.samples\.(ts|js)$/, '')}--${exportName.toLowerCase()}`;
            sampleEntries += `  '${sampleId}': {
    component: ${componentId},
    file: '${file}',
    export: '${exportName}',
    title: '${exportName}'
  },\n`;
        });
    });
    return `
${imports}

export const samples = {
${sampleEntries}};

export const errorMessage = '';
`;
}
function getAppHTML(appPath) {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Samplebook</title>
  <style>
    body { margin: 0; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; }
  </style>
</head>
<body>
  <div id="samplebook-root"></div>
  <script type="module" src="/@samplebook/app/index.tsx"></script>
</body>
</html>
  `.trim();
}
export default samplebookPlugin;
