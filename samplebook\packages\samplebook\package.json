{"name": "samplebook", "version": "1.0.0", "type": "module", "description": "A minimal Storybook clone for any project type", "main": "lib/index.js", "bin": {"samplebook": "bin/samplebook.js"}, "scripts": {"build": "tsc", "dev": "tsc --watch"}, "dependencies": {"commander": "^11.0.0", "vite": "^5.0.0", "@vitejs/plugin-react": "^4.0.0", "globby": "^14.0.0", "chokidar": "^3.5.0", "get-port": "^7.0.0", "boxen": "^7.0.0", "koa": "^2.14.0", "koa-connect": "^2.1.0", "@babel/parser": "^7.23.0", "@babel/traverse": "^7.23.0", "@babel/types": "^7.23.0", "@babel/generator": "^7.23.0", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"typescript": "^5.0.0", "@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/koa": "^2.13.0", "tsx": "^4.0.0"}}