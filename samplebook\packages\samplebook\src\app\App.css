* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
}

.app {
  display: flex;
  height: 100vh;
}

.sidebar {
  width: 300px;
  background: white;
  border-right: 1px solid #e0e0e0;
  overflow-y: auto;
  flex-shrink: 0;
}

.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid #e0e0e0;
  background: #fafafa;
}

.sidebar-header h1 {
  margin: 0;
  font-size: 24px;
  color: #333;
  font-weight: 600;
}

.sidebar-header p {
  margin: 5px 0 0 0;
  color: #666;
  font-size: 14px;
}

.sample-list {
  padding: 0;
  margin: 0;
  list-style: none;
}

.sample-item {
  border-bottom: 1px solid #f0f0f0;
}

.sample-button {
  width: 100%;
  padding: 15px 20px;
  border: none;
  background: white;
  text-align: left;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 14px;
}

.sample-button:hover {
  background-color: #f8f8f8;
}

.sample-button.active {
  background-color: #e3f2fd;
  border-right: 3px solid #2196f3;
}

.sample-title {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.sample-meta {
  font-size: 12px;
  color: #666;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.sample-header {
  padding: 20px;
  background: white;
  border-bottom: 1px solid #e0e0e0;
  flex-shrink: 0;
}

.sample-header h2 {
  margin: 0 0 5px 0;
  font-size: 20px;
  color: #333;
}

.sample-header .meta {
  font-size: 14px;
  color: #666;
}

.sample-container {
  flex: 1;
  padding: 20px;
  overflow: auto;
  background: white;
}

.sample-viewport {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  min-height: 400px;
  background: white;
  position: relative;
  overflow: hidden;
}

.sample-content {
  width: 100%;
  height: 100%;
  min-height: 400px;
}

.error-container,
.empty-container,
.no-selection {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  padding: 40px;
  text-align: center;
}

.error-container h1,
.empty-container h1,
.no-selection h2 {
  color: #333;
  margin-bottom: 20px;
}

.error-container pre {
  background: #f5f5f5;
  padding: 20px;
  border-radius: 8px;
  max-width: 600px;
  overflow: auto;
  text-align: left;
}

.example {
  margin-top: 30px;
  text-align: left;
  max-width: 500px;
}

.example h3 {
  margin-bottom: 10px;
  color: #333;
}

.example pre {
  background: #f8f8f8;
  padding: 15px;
  border-radius: 6px;
  border-left: 4px solid #2196f3;
  font-size: 13px;
  overflow-x: auto;
}
