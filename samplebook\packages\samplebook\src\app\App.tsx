import React, { useState, useEffect } from 'react';
import { samples, errorMessage } from 'virtual:samplebook-samples';
import Sidebar from './components/Sidebar';
import SampleRenderer from './components/SampleRenderer';
import './App.css';

interface Sample {
  component: () => HTMLElement;
  file: string;
  export: string;
  title: string;
}

const App: React.FC = () => {
  const [selectedSample, setSelectedSample] = useState<string | null>(null);
  const [sampleList, setSampleList] = useState<Record<string, Sample>>({});
  
  useEffect(() => {
    setSampleList(samples);
    
    // Auto-select first sample if available
    const sampleKeys = Object.keys(samples);
    if (sampleKeys.length > 0 && !selectedSample) {
      setSelectedSample(sampleKeys[0]);
    }
  }, [selectedSample]);
  
  if (errorMessage) {
    return (
      <div className="error-container">
        <h1>Error Loading <PERSON></h1>
        <pre>{errorMessage}</pre>
      </div>
    );
  }
  
  const sampleKeys = Object.keys(sampleList);
  
  if (sampleKeys.length === 0) {
    return (
      <div className="empty-container">
        <h1>No Samples Found</h1>
        <p>Create some .samples.ts files in your project to get started!</p>
        <div className="example">
          <h3>Example:</h3>
          <pre>{`// example.samples.ts
export function basicCube() {
  const div = document.createElement('div');
  div.textContent = 'Hello from a sample!';
  return div;
}`}</pre>
        </div>
      </div>
    );
  }
  
  return (
    <div className="app">
      <Sidebar
        samples={sampleList}
        selectedSample={selectedSample}
        onSelectSample={setSelectedSample}
      />
      <main className="main-content">
        {selectedSample && sampleList[selectedSample] ? (
          <SampleRenderer
            sample={sampleList[selectedSample]}
            sampleId={selectedSample}
          />
        ) : (
          <div className="no-selection">
            <h2>Select a sample to view</h2>
          </div>
        )}
      </main>
    </div>
  );
};

export default App;
