import React, { useEffect, useRef, useState } from 'react';

interface Sample {
  component: () => HTMLElement;
  file: string;
  export: string;
  title: string;
}

interface SampleRendererProps {
  sample: Sample;
  sampleId: string;
}

const SampleRenderer: React.FC<SampleRendererProps> = ({ sample, sampleId }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  
  useEffect(() => {
    if (!containerRef.current) return;
    
    const container = containerRef.current;
    setError(null);
    setIsLoading(true);
    
    // Clear previous content
    container.innerHTML = '';
    
    try {
      // Call the sample function to get the DOM element
      const element = sample.component();
      
      if (!element || !(element instanceof HTMLElement)) {
        throw new Error('Sample function must return an HTMLElement');
      }
      
      // Append the element to our container
      container.appendChild(element);
      setIsLoading(false);
      
    } catch (err) {
      console.error('Error rendering sample:', err);
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
      setIsLoading(false);
    }
    
    // Cleanup function
    return () => {
      if (container) {
        container.innerHTML = '';
      }
    };
  }, [sample, sampleId]);
  
  return (
    <>
      <div className="sample-header">
        <h2>{sample.title}</h2>
        <div className="meta">
          <span>{sample.file}</span> → <span>{sample.export}</span>
        </div>
      </div>
      
      <div className="sample-container">
        <div className="sample-viewport">
          {isLoading && (
            <div style={{ 
              display: 'flex', 
              alignItems: 'center', 
              justifyContent: 'center', 
              height: '100%',
              color: '#666'
            }}>
              Loading sample...
            </div>
          )}
          
          {error && (
            <div style={{ 
              padding: '20px', 
              color: '#d32f2f',
              backgroundColor: '#ffebee',
              border: '1px solid #ffcdd2',
              borderRadius: '4px',
              margin: '20px'
            }}>
              <h3>Error rendering sample:</h3>
              <pre>{error}</pre>
            </div>
          )}
          
          <div 
            ref={containerRef} 
            className="sample-content"
            style={{ display: error ? 'none' : 'block' }}
          />
        </div>
      </div>
    </>
  );
};

export default SampleRenderer;
