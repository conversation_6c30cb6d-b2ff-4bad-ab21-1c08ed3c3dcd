import React from 'react';

interface Sample {
  component: () => HTMLElement;
  file: string;
  export: string;
  title: string;
}

interface SidebarProps {
  samples: Record<string, Sample>;
  selectedSample: string | null;
  onSelectSample: (sampleId: string) => void;
}

const Sidebar: React.FC<SidebarProps> = ({ samples, selectedSample, onSelectSample }) => {
  const sampleEntries = Object.entries(samples);
  
  // Group samples by file
  const groupedSamples = sampleEntries.reduce((groups, [id, sample]) => {
    const file = sample.file;
    if (!groups[file]) {
      groups[file] = [];
    }
    groups[file].push({ id, sample });
    return groups;
  }, {} as Record<string, Array<{ id: string; sample: Sample }>>);
  
  return (
    <div className="sidebar">
      <div className="sidebar-header">
        <h1>🔬 Samplebook</h1>
        <p>{sampleEntries.length} samples found</p>
      </div>
      
      <ul className="sample-list">
        {Object.entries(groupedSamples).map(([file, fileSamples]) => (
          <li key={file} className="file-group">
            <div className="file-header">
              <strong>{file}</strong>
            </div>
            {fileSamples.map(({ id, sample }) => (
              <li key={id} className="sample-item">
                <button
                  className={`sample-button ${selectedSample === id ? 'active' : ''}`}
                  onClick={() => onSelectSample(id)}
                >
                  <div className="sample-title">{sample.title}</div>
                  <div className="sample-meta">{sample.export}</div>
                </button>
              </li>
            ))}
          </li>
        ))}
      </ul>
    </div>
  );
};

export default Sidebar;
