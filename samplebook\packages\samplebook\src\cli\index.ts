#!/usr/bin/env node

import { Command } from 'commander';
import serve from './serve.js';

const program = new Command('samplebook');
program.showHelpAfterError().showSuggestionAfterError();

program
  .command('serve')
  .alias('dev')
  .description('start developing')
  .argument('[hostRoot]', 'path to the host project root', '.')
  .option('-h, --host [string]', 'host to serve the application', 'localhost')
  .option('-p, --port [number]', 'port to serve the application', '3000')
  .option('--samples [string]', 'glob pattern to find sample files', '**/*.samples.{ts,js}')
  .option('--mode [string]', 'Vite mode', 'development')
  .action(serve);

// Default to serve command if no command specified
if (process.argv.length === 2 || !['serve', 'dev'].includes(process.argv[2])) {
  process.argv.splice(2, 0, 'serve');
}

program.parse(process.argv);
