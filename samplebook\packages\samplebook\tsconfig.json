{"compilerOptions": {"target": "ES2020", "module": "ESNext", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "strict": true, "noEmit": false, "declaration": true, "outDir": "lib", "rootDir": "src", "skipLibCheck": true, "types": ["node"], "forceConsistentCasingInFileNames": true, "jsx": "react-jsx"}, "include": ["src/**/*"], "exclude": ["node_modules", "lib"]}